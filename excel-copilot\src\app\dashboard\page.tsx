'use client';

import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Sparkles,
  FileSpreadsheet,
  BarChart,
  FileSpreadsheetIcon,
  Clock,
  Calendar,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Search,
  X,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

import { QuickActions, QuickTemplates, QuickAccess } from '@/components/dashboard/QuickActions';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { WorkbooksTable } from '@/components/dashboard/WorkbooksTable';
import { useCSRF } from '@/components/providers/csrf-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WorkbookActions } from '@/components/workbook-actions';
import { WorkbookTemplatesServer } from '@/components/workbook-templates';
// import { useDashboardMetricsRealtime } from '@/hooks/useDashboardMetrics'; // Removido - hook não existe mais
import { FormInputEvent, FormTextareaEvent } from '@/types/form-events';
import { Textarea, Input } from '@/types/ui-components';

// Dados iniciais para uma planilha em branco
const INITIAL_DATA = {
  headers: ['A', 'B', 'C', 'D', 'E'],
  rows: [
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
  ],
  charts: [] as any[],
  name: 'Nova Planilha',
};

// Exemplos de comandos de IA populares
const QUICK_COMMANDS = [
  {
    title: 'Planilha de Finanças',
    description: 'Crie uma planilha para controle de gastos mensais com categorias',
    icon: <FileSpreadsheetIcon className="h-6 w-6 text-green-500" />,
    command:
      'Criar planilha de controle financeiro pessoal com categorias de gastos e receitas e balanço mensal',
  },
  {
    title: 'Controle de Tarefas',
    description: 'Organize suas tarefas com datas, status e prioridades',
    icon: <FileSpreadsheetIcon className="h-6 w-6 text-blue-500" />,
    command:
      'Criar planilha de gerenciamento de tarefas com datas, status, responsáveis e prioridades',
  },
  {
    title: 'Dashboard de Vendas',
    description: 'Visualize dados de vendas com gráficos e tabelas dinâmicas',
    icon: <BarChart className="h-6 w-6 text-indigo-500" />,
    command:
      'Criar dashboard de vendas com tabela de dados, gráfico de barras para vendas mensais e gráfico de pizza para produtos',
  },
];

export default function DashboardPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  // Usando apenas o toast da biblioteca Sonner para todas as notificações
  const [timeOfDay, setTimeOfDay] = useState('');
  const [showCreationPanel, setShowCreationPanel] = useState(false);
  const [planilhaName, setPlanilhaName] = useState('');
  const [planilhaDescription, setPlanilhaDescription] = useState('');
  const [commandPrompt, setCommandPrompt] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const searchParams = useSearchParams();
  const { csrfToken } = useCSRF();
  const [recentWorkbooks, setRecentWorkbooks] = useState<any[]>([]);
  const [sharedWorkbooks, setSharedWorkbooks] = useState<any[]>([]);

  // Dados mockados para métricas (substituindo hook removido)
  const metrics = {
    recentActivity: [],
    weeklyActivity: []
  };
  const metricsLoading = false;
  const [isLoadingShared, setIsLoadingShared] = useState(false);
  const [_isDeleting, setIsDeleting] = useState<string | null>(null);
  const [errorMessages, setErrorMessages] = useState<Record<string, string>>({});
  const abortControllersRef = useRef<Record<string, AbortController>>({});
  const [pagination, setPagination] = useState({
    current: 0,
    total: 1,
    limit: 10,
    hasMore: false,
  });
  const [searchQuery, setSearchQuery] = useState('');

  // Verificar se há algum parâmetro de comando na URL
  useEffect(() => {
    if (searchParams) {
      const command = searchParams.get('command');
      if (command) {
        setCommandPrompt(command);
        setShowCreationPanel(true);
      }
    }
  }, [searchParams]);

  // Determinar o período do dia para saudação personalizada
  useEffect(() => {
    const hour = new Date().getHours();
    if (hour < 12) setTimeOfDay('Bom dia');
    else if (hour < 18) setTimeOfDay('Boa tarde');
    else setTimeOfDay('Boa noite');
  }, []);

  // Verificar autenticação
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=' + encodeURIComponent('/dashboard'));
    }
  }, [status, router]);

  // Função para limpar controllers na desmontagem
  useEffect(() => {
    return () => {
      // Cancelar todas as requisições pendentes quando o componente for desmontado
      // eslint-disable-next-line react-hooks/exhaustive-deps
      Object.values(abortControllersRef.current).forEach(controller => {
        controller.abort();
      });
    };
  }, []);

  // Função para iniciar a criação da planilha
  const handleStartCreation = () => {
    setShowCreationPanel(true);
  };

  // Função para criar planilha e redirecionar
  const handleCreateWorkbook = async () => {
    try {
      setIsCreating(true);

      // Verificar se ao menos um nome foi fornecido
      if (!planilhaName && !commandPrompt) {
        toast.error('Por favor, forneça um nome ou um comando para a planilha');
        setIsCreating(false);
        return;
      }

      // Criar a planilha via API
      const response = await fetch('/api/workbooks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
        },
        body: JSON.stringify({
          name: planilhaName || 'Nova Planilha',
          description: planilhaDescription,
          aiCommand: commandPrompt || null,
          initialData: INITIAL_DATA,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao criar planilha');
      }

      const data = await response.json();

      toast.success('Planilha criada com sucesso');

      // Redirecionar para a tela de edição, passando o comando se existir
      if (commandPrompt) {
        router.push(`/workbook/${data.workbook.id}?command=${encodeURIComponent(commandPrompt)}`);
      } else {
        router.push(`/workbook/${data.workbook.id}`);
      }
    } catch {
      // Erro ao criar planilha - não expor detalhes em produção
      toast.error('Não foi possível criar a planilha');
      setIsCreating(false);
    }
  };

  // Função para aplicar comando rápido
  const handleQuickCommand = (command: string) => {
    setCommandPrompt(command);
    setShowCreationPanel(true);
  };

  // Adicionar função para carregar planilhas recentes
  const fetchRecentWorkbooks = useCallback(
    async (page = 0) => {
      try {
        if (!session?.user) return;

        // Cancelar requisição anterior se existir
        if (abortControllersRef.current.recentWorkbooks) {
          abortControllersRef.current.recentWorkbooks.abort();
        }

        // Criar novo controller para esta requisição
        const controller = new AbortController();
        abortControllersRef.current.recentWorkbooks = controller;

        // Garantir que o CSRF token existe em produção
        if (process.env.NODE_ENV === 'production' && !csrfToken) {
          throw new Error('Token CSRF não disponível');
        }

        const response = await fetch(
          `/api/workbooks/recent?page=${page}&limit=${pagination.limit}`,
          {
            headers: {
              'Content-Type': 'application/json',
              ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
            },
            signal: controller.signal,
          }
        );

        // Remover controller após completar
        delete abortControllersRef.current.recentWorkbooks;

        if (!response.ok) {
          const errorData = await response.json();
          setErrorMessages(prev => ({
            ...prev,
            recentWorkbooks:
              errorData.details || errorData.error || 'Erro ao carregar planilhas recentes',
          }));
          return;
        }

        const data = await response.json();

        // Atualizar informações de paginação
        if (data.pagination) {
          setPagination({
            current: data.pagination.page,
            total: data.pagination.totalPages,
            limit: data.pagination.limit,
            hasMore: data.pagination.hasMore,
          });
        }

        // Formatar as datas
        const formattedWorkbooks = data.workbooks.map((wb: any) => ({
          ...wb,
          createdAt: new Date(wb.createdAt),
          updatedAt: new Date(wb.updatedAt),
          lastAccessedAt: new Date(wb.lastAccessedAt || wb.updatedAt),
          sheets: Array.isArray(wb.sheets) ? wb.sheets : wb.sheetsCount || 0,
        }));

        setRecentWorkbooks(formattedWorkbooks);
        // Limpar mensagem de erro se a requisição for bem-sucedida
        setErrorMessages(prev => ({ ...prev, recentWorkbooks: '' }));
      } catch (error) {
        // Não tratar erros de requisição abortada
        if (error instanceof DOMException && error.name === 'AbortError') {
          return;
        }

        console.error('Erro ao carregar planilhas recentes:', error);
        const errorMessage =
          error instanceof Error
            ? `Falha ao carregar planilhas: ${error.message}`
            : 'Erro ao carregar planilhas recentes';

        toast.error(errorMessage);
        setErrorMessages(prev => ({ ...prev, recentWorkbooks: errorMessage }));
      }
    },
    [session, csrfToken, pagination.limit]
  );

  // Carregar workbooks recentes ao montar componente
  useEffect(() => {
    if (session?.user) {
      fetchRecentWorkbooks(0);
    }
  }, [session, fetchRecentWorkbooks]);

  // Funções de navegação de páginas
  const handleNextPage = () => {
    if (pagination.hasMore) {
      fetchRecentWorkbooks(pagination.current + 1);
    }
  };

  const handlePrevPage = () => {
    if (pagination.current > 0) {
      fetchRecentWorkbooks(pagination.current - 1);
    }
  };

  // Adicionar função para carregar planilhas compartilhadas
  const fetchSharedWorkbooks = useCallback(async () => {
    try {
      if (!session?.user) return;

      setIsLoadingShared(true);

      // Cancelar requisição anterior se existir
      if (abortControllersRef.current.sharedWorkbooks) {
        abortControllersRef.current.sharedWorkbooks.abort();
      }

      // Criar novo controller para esta requisição
      const controller = new AbortController();
      abortControllersRef.current.sharedWorkbooks = controller;

      const response = await fetch('/api/workbooks/shared', {
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
        },
        signal: controller.signal,
      });

      // Remover controller após completar
      delete abortControllersRef.current.sharedWorkbooks;

      if (!response.ok) {
        const errorData = await response.json();
        setErrorMessages(prev => ({
          ...prev,
          sharedWorkbooks:
            errorData.details || errorData.error || 'Erro ao carregar planilhas compartilhadas',
        }));
        return;
      }

      const data = await response.json();

      // Formatar as datas
      const formattedWorkbooks = data.workbooks.map((wb: any) => ({
        ...wb,
        createdAt: new Date(wb.createdAt),
        updatedAt: new Date(wb.updatedAt),
        sharedAt: new Date(wb.sharedAt),
        sheets: Array.isArray(wb.sheets) ? wb.sheets : wb.sheetsCount || 0,
      }));

      setSharedWorkbooks(formattedWorkbooks);
      // Limpar mensagem de erro se a requisição for bem-sucedida
      setErrorMessages(prev => ({ ...prev, sharedWorkbooks: '' }));
    } catch (error) {
      // Não tratar erros de requisição abortada
      if (error instanceof DOMException && error.name === 'AbortError') {
        return;
      }

      console.error('Erro ao carregar planilhas compartilhadas:', error);
      const errorMessage =
        error instanceof Error
          ? `Falha ao carregar planilhas compartilhadas: ${error.message}`
          : 'Erro ao carregar planilhas compartilhadas';

      toast.error(errorMessage);
      setErrorMessages(prev => ({ ...prev, sharedWorkbooks: errorMessage }));
    } finally {
      setIsLoadingShared(false);
    }
  }, [session, csrfToken]);

  // Carregar workbooks compartilhados ao montar componente
  useEffect(() => {
    if (session?.user) {
      fetchSharedWorkbooks();
    }
  }, [session, fetchSharedWorkbooks]);

  // Função para formatar data relativa em português
  const formatRelativeDate = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
  };

  // Função para abrir uma planilha
  const handleOpenWorkbook = (workbookId: string) => {
    router.push(`/workbook/${workbookId}`);
  };

  // Função para duplicar uma planilha
  const _handleDuplicateWorkbook = async (workbookId: string) => {
    try {
      const response = await fetch(`/api/workbooks/${workbookId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao duplicar planilha');
      }

      toast.success(`Planilha duplicada com sucesso!`);
      // Recarregar as listas de planilhas
      if (session?.user) {
        fetchRecentWorkbooks(pagination.current);
        fetchSharedWorkbooks();
      }
    } catch (error) {
      console.error('Erro ao duplicar workbook:', error);
      toast.error('Não foi possível duplicar a planilha');
    }
  };

  // Função para excluir uma planilha
  const _handleDeleteWorkbook = async (workbookId: string) => {
    try {
      setIsDeleting(workbookId);

      const response = await fetch(`/api/workbooks/${workbookId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao excluir planilha');
      }

      toast.success('Planilha excluída com sucesso!');
      // Recarregar as listas de planilhas
      if (session?.user) {
        fetchRecentWorkbooks(pagination.current);
        fetchSharedWorkbooks();
      }
    } catch (error) {
      console.error('Erro ao excluir workbook:', error);
      toast.error('Não foi possível excluir a planilha');
    } finally {
      setIsDeleting(null);
    }
  };

  // Adicionar componente de paginação ao final da tabela de workbooks recentes
  const PaginationControls = () => (
    <div className="flex items-center justify-between mt-4">
      <p className="text-sm text-muted-foreground">
        Página {pagination.current + 1} de {Math.max(1, pagination.total)}
      </p>
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevPage}
          disabled={pagination.current <= 0}
        >
          <ChevronLeft className="h-4 w-4 mr-1" /> Anterior
        </Button>
        <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!pagination.hasMore}>
          Próxima <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );

  // Renderizar loader enquanto verifica autenticação
  if (status === 'loading') {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  // Não renderiza nada se não estiver autenticado (será redirecionado pelo useEffect)
  if (status === 'unauthenticated') {
    return null;
  }

  if (showCreationPanel) {
    return (
      <main className="container mx-auto py-6 px-4 max-w-3xl">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Criar Nova Planilha</h1>
          <Button variant="ghost" onClick={() => setShowCreationPanel(false)}>
            Voltar
          </Button>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">Informações Básicas</CardTitle>
            <CardDescription>Defina os detalhes da sua planilha</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome da Planilha</Label>
              <Input
                id="name"
                placeholder="Ex: Controle Financeiro 2023"
                value={planilhaName}
                onChange={(e: FormInputEvent) => setPlanilhaName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição (opcional)</Label>
              <Textarea
                id="description"
                placeholder="Descreva o propósito desta planilha..."
                rows={2}
                value={planilhaDescription}
                onChange={(e: FormTextareaEvent) => setPlanilhaDescription(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              Assistente IA
            </CardTitle>
            <CardDescription>
              Descreva o que você deseja criar e deixe a IA fazer o trabalho
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="prompt">Comando para IA (opcional)</Label>
              <Textarea
                id="prompt"
                placeholder="Ex: Crie uma planilha de controle financeiro com categorias de gastos e gráficos..."
                rows={3}
                value={commandPrompt}
                onChange={(e: FormTextareaEvent) => setCommandPrompt(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Descreva em linguagem natural o que você quer criar. Quanto mais detalhes, melhor
                será o resultado.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => setCommandPrompt('')}
              disabled={!commandPrompt}
            >
              Limpar
            </Button>
            <Button
              onClick={() => handleCreateWorkbook()}
              disabled={isCreating}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Criando...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Criar Nova Planilha
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <div className="mt-8">
          <h2 className="text-lg font-medium mb-4">
            Dica: você também pode usar templates pré-definidos
          </h2>
          <WorkbookTemplatesServer />
        </div>
      </main>
    );
  }

  return (
    <main className="container mx-auto py-6 px-4 max-w-7xl">
      {/* Header com boas-vindas personalizada */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 mb-8 shadow-sm">
        <div className="flex flex-col md:flex-row md:items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gradient bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400">
              {timeOfDay}, {session?.user?.name?.split(' ')[0] || 'Bem-vindo'}!
            </h1>
            <p className="text-muted-foreground mt-2 max-w-xl">
              Pronto para turbinar suas planilhas? Use o Excel Copilot com IA para criar, editar e
              analisar dados de forma inteligente.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              size="lg"
              onClick={() => handleStartCreation()}
              className="mt-4 md:mt-0 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all duration-300 hover:shadow-lg"
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Criar Nova Planilha
            </Button>
          </div>
        </div>
      </div>

      {/* Comandos Rápidos / Sugestões */}
      <div className="mb-8">
        <h2 className="text-xl font-medium mb-4">Criar Rapidamente</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {QUICK_COMMANDS.map((cmd, index) => (
            <Card
              key={index}
              className="cursor-pointer hover:shadow-md transition-all border-2 hover:border-primary/20"
              onClick={() => handleQuickCommand(cmd.command)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  {cmd.icon}
                  {cmd.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{cmd.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Widgets de Métricas removidos - componentes não existem mais */}

      {/* Layout em Grid para Atividade Recente e Quick Actions - PRIORIDADE MÉDIA */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Atividade Recente - 2/3 da largura */}
        <div className="lg:col-span-2">
          <RecentActivity
            activities={metrics?.recentActivity || []}
            isLoading={metricsLoading}
            maxItems={8}
          />
        </div>

        {/* Quick Actions - 1/3 da largura */}
        <div className="space-y-6">
          <QuickActions
            onCreateWorkbook={() => setShowCreationPanel(true)}
            recentWorkbooks={recentWorkbooks}
          />

          <QuickTemplates />

          <QuickAccess recentWorkbooks={recentWorkbooks} />
        </div>
      </div>

      {/* Gráficos de Atividade removidos - componente não existe mais */}

      {/* Lista de planilhas do usuário */}
      <div>
        {/* Barra de busca */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Buscar planilhas..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="all">Todas as Planilhas</TabsTrigger>
              <TabsTrigger value="recent">Recentes</TabsTrigger>
              <TabsTrigger value="shared">Compartilhadas</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="mt-0">
            <WorkbooksTable searchQuery={searchQuery} />
          </TabsContent>

          <TabsContent value="recent" className="mt-0">
            {errorMessages.recentWorkbooks ? (
              <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Erro ao carregar planilhas recentes
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <p>{errorMessages.recentWorkbooks}</p>
                    </div>
                    <div className="mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => fetchRecentWorkbooks(pagination.current)}
                        className="text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40"
                      >
                        Tentar novamente
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
            {recentWorkbooks.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Último acesso</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentWorkbooks.map(workbook => (
                      <TableRow
                        key={workbook.id}
                        className="cursor-pointer hover:bg-muted/40"
                        onClick={() => handleOpenWorkbook(workbook.id)}
                      >
                        <TableCell className="font-medium flex items-center gap-2">
                          <FileSpreadsheet className="h-4 w-4 text-blue-500" />
                          {workbook.name}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatRelativeDate(workbook.lastAccessedAt || workbook.updatedAt)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <WorkbookActions
                            workbookId={workbook.id}
                            onComplete={fetchRecentWorkbooks}
                            buttonVariant="ghost"
                            buttonSize="icon"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <PaginationControls />
              </div>
            ) : (
              <EmptyState
                icon={<FileSpreadsheet className="h-10 w-10" />}
                title="Nenhuma planilha recente"
                description="As planilhas que você acessou recentemente aparecerão aqui."
              />
            )}
          </TabsContent>

          <TabsContent value="shared" className="mt-0">
            {errorMessages.sharedWorkbooks ? (
              <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Erro ao carregar planilhas compartilhadas
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <p>{errorMessages.sharedWorkbooks}</p>
                    </div>
                    <div className="mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={fetchSharedWorkbooks}
                        className="text-sm text-red-800 dark:text-red-200 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/40"
                      >
                        Tentar novamente
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
            {isLoadingShared ? (
              <div className="w-full py-10 flex justify-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : sharedWorkbooks.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Compartilhado por</TableHead>
                      <TableHead>Data de compartilhamento</TableHead>
                      <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sharedWorkbooks.map(workbook => (
                      <TableRow
                        key={workbook.id}
                        className="cursor-pointer hover:bg-muted/40"
                        onClick={() => handleOpenWorkbook(workbook.id)}
                      >
                        <TableCell className="font-medium flex items-center gap-2">
                          <FileSpreadsheet className="h-4 w-4 text-blue-500" />
                          {workbook.name}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {workbook.sharedBy.image ? (
                              <Image
                                src={workbook.sharedBy.image}
                                alt={workbook.sharedBy.name || 'Usuário'}
                                width={24}
                                height={24}
                                className="h-6 w-6 rounded-full"
                              />
                            ) : (
                              <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                                {workbook.sharedBy.name?.charAt(0) || '?'}
                              </div>
                            )}
                            <span>{workbook.sharedBy.name || workbook.sharedBy.email}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatRelativeDate(workbook.sharedAt)}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <WorkbookActions
                            workbookId={workbook.id}
                            onComplete={fetchSharedWorkbooks}
                            onlyEdit={true}
                            buttonVariant="ghost"
                            buttonSize="sm"
                            allowDelete={false}
                            allowDuplicate={false}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <EmptyState
                icon={<FileSpreadsheet className="h-10 w-10" />}
                title="Nenhuma planilha compartilhada"
                description="As planilhas compartilhadas com você aparecerão aqui."
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </main>
  );
}
