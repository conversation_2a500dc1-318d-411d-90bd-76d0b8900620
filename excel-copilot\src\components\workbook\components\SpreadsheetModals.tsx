'use client';

import React from 'react';
import { Keyboard, Crown, Star, MessageSquare } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface KeyboardShortcut {
  keys: string[];
  description: string;
  mac: string[];
}

interface SpreadsheetModalsProps {
  // Estados dos modais
  showKeyboardShortcuts: boolean;
  showUpgradeModal: boolean;
  showFeedback: boolean;
  showTutorial: boolean;
  tutorialStep: number;

  // Dados
  keyboardShortcuts: KeyboardShortcut[];

  // Handlers
  onCloseKeyboardShortcuts: () => void;
  onCloseUpgradeModal: () => void;
  onCloseFeedback: () => void;
  onCloseTutorial: () => void;
  onNextTutorialStep: () => void;
  onUpgradeProClick: () => void;
  onTrialClick: () => void;
}

export function SpreadsheetModals({
  showKeyboardShortcuts,
  showUpgradeModal,
  showFeedback,
  showTutorial,
  tutorialStep,
  keyboardShortcuts,
  onCloseKeyboardShortcuts,
  onCloseUpgradeModal,
  onCloseFeedback,
  onCloseTutorial,
  onNextTutorialStep,
  onUpgradeProClick,
  onTrialClick,
}: SpreadsheetModalsProps) {
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;

  return (
    <>
      {/* Modal de Atalhos de Teclado */}
      <Dialog open={showKeyboardShortcuts} onOpenChange={onCloseKeyboardShortcuts}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Keyboard className="h-5 w-5" />
              <span>Atalhos de Teclado</span>
            </DialogTitle>
            <DialogDescription>
              Use estes atalhos para trabalhar mais rapidamente com sua planilha
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            {keyboardShortcuts.map((shortcut, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                <span className="text-sm">{shortcut.description}</span>
                <div className="flex space-x-1">
                  {(isMac ? shortcut.mac : shortcut.keys).map((key, keyIndex) => (
                    <Badge key={keyIndex} variant="secondary" className="text-xs">
                      {key}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de Upgrade */}
      <Dialog open={showUpgradeModal} onOpenChange={onCloseUpgradeModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              <span>Upgrade para Pro</span>
            </DialogTitle>
            <DialogDescription>
              Você está próximo do limite de comandos IA. Faça upgrade para continuar usando todos os recursos.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Excel Copilot Pro</CardTitle>
                <CardDescription>Recursos ilimitados para profissionais</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>Comandos IA ilimitados</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>Colaboração em tempo real</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>Gráficos avançados</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>Suporte prioritário</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <div className="flex space-x-2">
              <Button onClick={onUpgradeProClick} className="flex-1">
                Upgrade Pro
              </Button>
              <Button onClick={onTrialClick} variant="outline" className="flex-1">
                Teste Grátis
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de Feedback */}
      <Dialog open={showFeedback} onOpenChange={onCloseFeedback}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Enviar Feedback</span>
            </DialogTitle>
            <DialogDescription>
              Ajude-nos a melhorar o Excel Copilot com seu feedback
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <textarea
              className="w-full h-32 p-3 border rounded-lg resize-none"
              placeholder="Conte-nos sobre sua experiência, sugestões ou problemas encontrados..."
            />

            <div className="flex space-x-2">
              <Button className="flex-1">Enviar Feedback</Button>
              <Button variant="outline" onClick={onCloseFeedback}>
                Cancelar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Tutorial Modal */}
      <Dialog open={showTutorial} onOpenChange={onCloseTutorial}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              Bem-vindo ao Excel Copilot! ({tutorialStep + 1}/4)
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {tutorialStep === 0 && (
              <div>
                <h3 className="font-semibold mb-2">🎉 Primeira vez aqui?</h3>
                <p className="text-sm text-muted-foreground">
                  O Excel Copilot é uma planilha inteligente que entende comandos em linguagem natural.
                  Vamos fazer um tour rápido!
                </p>
              </div>
            )}

            {tutorialStep === 1 && (
              <div>
                <h3 className="font-semibold mb-2">🤖 Assistente IA</h3>
                <p className="text-sm text-muted-foreground">
                  Use o painel à direita para dar comandos como "Adicione uma coluna Total" ou 
                  "Calcule a média dos valores". A IA entenderá e executará automaticamente!
                </p>
              </div>
            )}

            {tutorialStep === 2 && (
              <div>
                <h3 className="font-semibold mb-2">⌨️ Atalhos Úteis</h3>
                <p className="text-sm text-muted-foreground">
                  Pressione Ctrl+K para abrir a paleta de comandos, Ctrl+S para salvar,
                  e ? para ver todos os atalhos disponíveis.
                </p>
              </div>
            )}

            {tutorialStep === 3 && (
              <div>
                <h3 className="font-semibold mb-2">🚀 Pronto para começar!</h3>
                <p className="text-sm text-muted-foreground">
                  Agora você está pronto para usar o Excel Copilot. Comece digitando alguns dados
                  ou use comandos IA para criar sua planilha automaticamente!
                </p>
              </div>
            )}

            <Separator />

            <div className="flex justify-between">
              <Button variant="outline" onClick={onCloseTutorial}>
                Pular Tutorial
              </Button>
              <Button onClick={onNextTutorialStep}>
                {tutorialStep === 3 ? 'Começar!' : 'Próximo'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
