'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ChatInterface } from '@/components/chat-interface';

interface AIAssistantPanelProps {
  // Estados
  collapsed: boolean;
  showAiIndicator: boolean;
  showSuggestions: boolean;
  inputText: string;
  isProcessing: boolean;
  readOnly: boolean;

  // Dados
  spreadsheetData: any;
  apiUsageInfo?: {
    used: number;
    limit: number;
  } | null;

  // Handlers
  onInputChange: (text: string) => void;
  onProcessCommand: (command: string) => void;
  onUpgradeClick: () => void;
  onTrialClick: () => void;
}

export function AIAssistantPanel({
  collapsed,
  showAiIndicator,
  showSuggestions,
  inputText,
  isProcessing,
  readOnly,
  spreadsheetData,
  apiUsageInfo,
  onInputChange,
  onProcessCommand,
  onUpgradeClick,
  onTrialClick,
}: AIAssistantPanelProps) {
  // Se o painel estiver colapsado, não renderizar
  if (collapsed) {
    return null;
  }

  // Verificar se está próximo do limite da API
  const isNearLimit = apiUsageInfo && apiUsageInfo.used / apiUsageInfo.limit >= 0.8;
  const isOverLimit = apiUsageInfo && apiUsageInfo.used >= apiUsageInfo.limit;

  return (
    <div className="w-80 border-l bg-background flex flex-col">
      {/* Header do painel */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <h3 className="font-semibold">Assistente IA</h3>
          {showAiIndicator && (
            <div className="flex items-center space-x-1 text-sm text-primary">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
              <span>Processando...</span>
            </div>
          )}
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          Use comandos em linguagem natural para manipular sua planilha
        </p>
      </div>

      {/* Alertas de uso da API */}
      {isOverLimit && (
        <div className="p-4 border-b">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Você atingiu o limite de comandos IA. Faça upgrade para continuar usando.
            </AlertDescription>
          </Alert>
          <div className="flex space-x-2 mt-3">
            <Button size="sm" onClick={onUpgradeClick} className="flex-1">
              Upgrade Pro
            </Button>
            <Button size="sm" variant="outline" onClick={onTrialClick} className="flex-1">
              Teste Grátis
            </Button>
          </div>
        </div>
      )}

      {isNearLimit && !isOverLimit && (
        <div className="p-4 border-b">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Você usou {apiUsageInfo?.used} de {apiUsageInfo?.limit} comandos IA este mês.
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Interface do Chat */}
      <div className="flex-1 flex flex-col">
        <ChatInterface
          data={spreadsheetData}
          onOperations={onProcessCommand}
          disabled={readOnly || isOverLimit}
          placeholder={
            readOnly
              ? 'Modo somente leitura'
              : isOverLimit
              ? 'Limite de comandos atingido'
              : 'Digite um comando para a IA...'
          }
          inputValue={inputText}
          onInputChange={onInputChange}
          isProcessing={isProcessing}
        />
      </div>

      {/* Sugestões rápidas */}
      {showSuggestions && !readOnly && !isOverLimit && (
        <div className="p-4 border-t">
          <h4 className="text-sm font-medium mb-3">Comandos sugeridos:</h4>
          <div className="space-y-2">
            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-3">
                <button
                  onClick={() => onProcessCommand('Adicione uma coluna chamada "Total"')}
                  className="w-full text-left"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Adicionar coluna "Total"</span>
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                  </div>
                </button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-3">
                <button
                  onClick={() => onProcessCommand('Calcule a soma da primeira coluna')}
                  className="w-full text-left"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Calcular soma da coluna</span>
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                  </div>
                </button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-3">
                <button
                  onClick={() => onProcessCommand('Ordene os dados por ordem alfabética')}
                  className="w-full text-left"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Ordenar dados</span>
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                  </div>
                </button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
              <CardContent className="p-3">
                <button
                  onClick={() => onProcessCommand('Crie um gráfico com os dados')}
                  className="w-full text-left"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Criar gráfico</span>
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                  </div>
                </button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-4 text-xs text-muted-foreground">
            💡 Dica: Use Ctrl+K para abrir a paleta de comandos
          </div>
        </div>
      )}
    </div>
  );
}
