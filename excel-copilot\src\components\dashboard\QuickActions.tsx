'use client';

import {
  Plus,
  Upload,
  Copy,
  FileSpreadsheet,
  Sparkles,
  Users,
  BarChart3,
  Calculator,
  PiggyBank
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void | Promise<void>;
  variant?: 'default' | 'secondary' | 'outline';
  disabled?: boolean;
  badge?: string;
}

interface QuickActionsProps {
  className?: string;
  onCreateWorkbook?: () => void;
  onImportFile?: () => void;
  recentWorkbooks?: Array<{
    id: string;
    name: string;
    updatedAt: Date;
  }>;
}

/**
 * Componente de ações rápidas para o dashboard
 */
export function QuickActions({ 
  className, 
  onCreateWorkbook,
  onImportFile,
  recentWorkbooks = []
}: QuickActionsProps) {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  // Ação para criar nova planilha
  const handleCreateWorkbook = async () => {
    if (onCreateWorkbook) {
      onCreateWorkbook();
      return;
    }

    try {
      setIsCreating(true);
      router.push('/dashboard?create=true');
    } catch (_error) {
      toast.error('Erro ao criar planilha');
    } finally {
      setIsCreating(false);
    }
  };

  // Ação para importar arquivo
  const handleImportFile = async () => {
    if (onImportFile) {
      onImportFile();
      return;
    }

    try {
      setIsImporting(true);
      // Criar input file temporário
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.xlsx,.xls,.csv';
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          toast.success(`Arquivo ${file.name} selecionado para importação`);
          // Aqui você implementaria a lógica de upload
        }
      };
      input.click();
    } catch (_error) {
      toast.error('Erro ao importar arquivo');
    } finally {
      setIsImporting(false);
    }
  };

  // Ação para duplicar planilha recente
  const handleDuplicateRecent = async () => {
    if (recentWorkbooks.length === 0) {
      toast.error('Nenhuma planilha recente encontrada');
      return;
    }

    const mostRecent = recentWorkbooks[0];
    if (!mostRecent) {
      toast.error('Nenhuma planilha recente encontrada');
      return;
    }

    try {
      toast.success(`Duplicando planilha "${mostRecent.name}"`);
      // Implementar lógica de duplicação
    } catch (_error) {
      toast.error('Erro ao duplicar planilha');
    }
  };

  // Ação para criar com IA
  const handleCreateWithAI = () => {
    router.push('/dashboard?create=true&ai=true');
  };

  // Ação para acessar templates
  const handleAccessTemplates = () => {
    router.push('/dashboard?templates=true');
  };

  // Ação para colaborar
  const handleCollaborate = () => {
    router.push('/dashboard?tab=shared');
  };

  // Definir ações principais
  const primaryActions: QuickAction[] = [
    {
      id: 'create',
      title: 'Nova Planilha',
      description: 'Criar planilha em branco',
      icon: <Plus className="h-5 w-5" />,
      action: handleCreateWorkbook,
      disabled: isCreating
    },
    {
      id: 'ai-create',
      title: 'Criar com IA',
      description: 'Usar assistente inteligente',
      icon: <Sparkles className="h-5 w-5" />,
      action: handleCreateWithAI,
      badge: 'IA'
    },
    {
      id: 'import',
      title: 'Importar Arquivo',
      description: 'Excel, CSV ou outros formatos',
      icon: <Upload className="h-5 w-5" />,
      action: handleImportFile,
      variant: 'outline',
      disabled: isImporting
    },
    {
      id: 'duplicate',
      title: 'Duplicar Recente',
      description: 'Copiar última planilha editada',
      icon: <Copy className="h-5 w-5" />,
      action: handleDuplicateRecent,
      variant: 'outline',
      disabled: recentWorkbooks.length === 0
    }
  ];

  // Definir ações secundárias
  const secondaryActions: QuickAction[] = [
    {
      id: 'templates',
      title: 'Templates',
      description: 'Modelos prontos',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      action: handleAccessTemplates,
      variant: 'secondary'
    },
    {
      id: 'collaborate',
      title: 'Colaborar',
      description: 'Planilhas compartilhadas',
      icon: <Users className="h-4 w-4" />,
      action: handleCollaborate,
      variant: 'secondary'
    }
  ];

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="text-lg">Ações Rápidas</CardTitle>
        <CardDescription>
          Comece rapidamente com suas planilhas
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Ações principais */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {primaryActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant || 'default'}
              className={cn(
                "h-auto p-4 flex flex-col items-start space-y-2 relative",
                action.disabled && "opacity-50 cursor-not-allowed"
              )}
              onClick={action.action}
              disabled={action.disabled}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center space-x-2">
                  {action.icon}
                  <span className="font-medium">{action.title}</span>
                </div>
                {action.badge && (
                  <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded-full">
                    {action.badge}
                  </span>
                )}
              </div>
              <p className="text-xs text-left opacity-80">
                {action.description}
              </p>
            </Button>
          ))}
        </div>

        {/* Ações secundárias */}
        <div className="flex flex-wrap gap-2">
          {secondaryActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant || 'secondary'}
              size="sm"
              className="flex items-center space-x-2"
              onClick={action.action}
              disabled={action.disabled}
            >
              {action.icon}
              <span>{action.title}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Componente de templates rápidos
 */
export function QuickTemplates({ className }: { className?: string }) {
  const router = useRouter();

  const templates = [
    {
      id: 'financial',
      name: 'Controle Financeiro',
      icon: <PiggyBank className="h-4 w-4" />,
      color: 'text-green-600'
    },
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <BarChart3 className="h-4 w-4" />,
      color: 'text-blue-600'
    },
    {
      id: 'calculator',
      name: 'Calculadora',
      icon: <Calculator className="h-4 w-4" />,
      color: 'text-purple-600'
    }
  ];

  const handleTemplateSelect = (templateId: string) => {
    router.push(`/dashboard?template=${templateId}`);
    toast.success('Carregando template...');
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="text-base">Templates Populares</CardTitle>
        <CardDescription>
          Comece com modelos prontos
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          {templates.map((template) => (
            <Button
              key={template.id}
              variant="ghost"
              className="w-full justify-start h-auto p-3"
              onClick={() => handleTemplateSelect(template.id)}
            >
              <div className={cn("mr-3", template.color)}>
                {template.icon}
              </div>
              <span className="text-sm">{template.name}</span>
            </Button>
          ))}
        </div>
        
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full mt-3"
          onClick={() => router.push('/dashboard?templates=true')}
        >
          Ver Todos os Templates
        </Button>
      </CardContent>
    </Card>
  );
}

/**
 * Componente de planilhas favoritas/recentes
 */
export function QuickAccess({ 
  recentWorkbooks = [],
  className 
}: { 
  recentWorkbooks?: Array<{
    id: string;
    name: string;
    updatedAt: Date;
  }>;
  className?: string;
}) {
  const router = useRouter();

  const handleOpenWorkbook = (workbookId: string) => {
    router.push(`/workbook/${workbookId}`);
  };

  if (recentWorkbooks.length === 0) {
    return null;
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="text-base">Acesso Rápido</CardTitle>
        <CardDescription>
          Suas planilhas mais recentes
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          {recentWorkbooks.slice(0, 3).map((workbook) => (
            <Button
              key={workbook.id}
              variant="ghost"
              className="w-full justify-start h-auto p-3"
              onClick={() => handleOpenWorkbook(workbook.id)}
            >
              <FileSpreadsheet className="h-4 w-4 mr-3 text-primary" />
              <div className="flex-1 text-left">
                <p className="text-sm font-medium truncate">
                  {workbook.name}
                </p>
                <p className="text-xs text-muted-foreground">
                  Editado {new Date(workbook.updatedAt).toLocaleDateString('pt-BR')}
                </p>
              </div>
            </Button>
          ))}
        </div>
        
        {recentWorkbooks.length > 3 && (
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full mt-3"
            onClick={() => router.push('/dashboard?tab=recent')}
          >
            Ver Todas ({recentWorkbooks.length})
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
